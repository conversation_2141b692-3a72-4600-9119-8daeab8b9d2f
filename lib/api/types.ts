export interface APIError {
  type: string;
  message: string;
  stacktrace?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  accessToken: string;
  expiresInSeconds: number;
}

export interface SignupRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface SignupResponse {
  accessToken: string;
  expiresInSeconds: number;
}

export interface User {
  firstName: string;
  lastName: string;
  email: string;
  status: "active" | "inactive";
}

export interface ProfileResponse {
  user: User;
}

export interface SpeechToTextResponse {
  text: string;
  confidence?: number;
}

// Type for the parsed SOAP note content from context.result
export interface TranscriptionSoapResult {
  clinicalNoteText: string;
  transcriptionText: string;
}

// Base interface for all context types
interface BaseCardFileContext {
  id: string;
  createdAt: string;
}

// Specific context type for transcription with SOAP notes
export interface TranscriptionSoapContext extends BaseCardFileContext {
  contextType: "transcription_soap";
  result: string; // JSON string that parses to TranscriptionSoapResult
}

// Add more context types as needed in the future
// export interface ImageAnalysisContext extends BaseCardFileContext {
//   contextType: "image_analysis";
//   result: string; // JSON string for image analysis results
// }

// Union type for all possible contexts
export type CardFileContext = TranscriptionSoapContext; // | ImageAnalysisContext | etc...

export interface FileUploaderRequest {
  fileName: string;
  contentType: string;
}

export interface FileUploaderResponse {
  url: string;
  objectName: string;
}
