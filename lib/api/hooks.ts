import { useMutation, useQuery } from "@tanstack/react-query";
import * as SecureStore from "expo-secure-store";

import { EXPIRY_KEY, TOKEN_KEY, triggerSignOut } from "@/context/AuthContext";
import { router } from "expo-router";
import { queryClient } from "../providers/query-client";
import type {
  APIError,
  CardFileContext,
  FileUploaderRequest,
  FileUploaderResponse,
  LoginRequest,
  LoginResponse,
  ProfileResponse,
  SignupRequest,
  SignupResponse,
  SpeechToTextResponse,
} from "./types";

// Card types
export interface Card {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export interface CardsResponse {
  cards: Card[];
}

export interface CardResponse {
  card: Card;
}

export interface CreateCardRequest {
  name: string;
}

export interface CardFile {
  id: string;
  cardId: string;
  analysisType: string; // e.g., "clinical_note", "image_analysis", etc.
  filePath: string;
  fileName: string;
  fileSize: number;
  createdAt: string;
  updatedAt: string;
  status: "pending" | "processing" | "completed" | "failed";
  /**
   * A time-limited presigned URL that can be used to download/play the file directly.
   * This is now returned by the backend and should be preferred over constructing
   * a URL from filePath.
   */
  signedUrl?: string;
}

export interface CardFilesResponse {
  cardFiles: CardFile[];
}

export interface AnalyzeFileResponse {
  fileId: string;
}

export interface AnalyzeFileRequest {
  cardId: string;
  audioUri: string;
  analysisType: string; // currently "clinical_note"
}

export interface AnalyzeFileWithObjectNameRequest {
  analysisType: string;
  objectName: string;
}

export interface CardFileDetail extends CardFile {
  contexts: CardFileContext[];
}

export interface CardFileResponse {
  cardFile: CardFileDetail;
}

// LLM conversation types
export interface ConversationMessagePair {
  query: string; // user prompt
  answer: string; // assistant reply
}

export interface ConversationMessagesResponse {
  messages: ConversationMessagePair[];
  conversationId: string;
  hasMore: boolean;
  limit: number;
}

export interface ChatMessageRequest {
  query: string;
  conversation_id?: string;
}

export interface ChatMessageResponse {
  message: ConversationMessagePair;
  conversationId: string;
}

const API_URL = process.env.EXPO_PUBLIC_API_DOMAIN || "http://localhost:3000";

class APIClient {
  private static async getStoredToken(): Promise<string | null> {
    try {
      const [token, expiryStr] = await Promise.all([
        SecureStore.getItemAsync(TOKEN_KEY),
        SecureStore.getItemAsync(EXPIRY_KEY),
      ]);

      if (!token || !expiryStr) return null;

      // const expiry = parseInt(expiryStr, 10);
      // if (Date.now() >= expiry) {
      //   await this.clearToken();
      //   return null;
      // }

      return token;
    } catch (error) {
      console.error("Error getting stored token:", error);
      return null;
    }
  }

  private static async saveToken(
    token: string,
    expiresInSeconds: number
  ): Promise<void> {
    const expiryTime = Date.now() + expiresInSeconds * 1000;
    await Promise.all([
      SecureStore.setItemAsync(TOKEN_KEY, token),
      SecureStore.setItemAsync(EXPIRY_KEY, expiryTime.toString()),
    ]);
  }

  private static async clearToken(): Promise<void> {
    await Promise.all([
      SecureStore.deleteItemAsync(TOKEN_KEY),
      SecureStore.deleteItemAsync(EXPIRY_KEY),
    ]);

    triggerSignOut();
  }

  private static async request<T>(
    path: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = await this.getStoredToken();
    console.log("token in request", token);

    const headers: Record<string, string> = {
      ...(options.headers as Record<string, string>),
    };

    // Check if body is FormData instance
    const isFormData = options.body instanceof FormData;

    // Only set Content-Type if not FormData (let browser set boundary for multipart)
    if (!isFormData && !headers["Content-Type"]) {
      headers["Content-Type"] = "application/json";
    }

    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    console.log(`Making ${options.method || "GET"} request to ${path}`, {
      isFormData,
      headers,
    });

    const response = await fetch(`${API_URL}${path}`, {
      ...options,
      headers,
    });

    // Handle unauthorized globally
    if (response.status === 401) {
      console.log("Unauthorized, logging out");
      this.logout();
    }

    if (!response.ok) {
      const error: APIError = await response.json();
      throw new Error(error.message || "API request failed");
    }

    return response.json();
  }

  static async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await this.request<LoginResponse>("/v1/login", {
      method: "POST",
      body: JSON.stringify(data),
    });

    console.log("login response", response);

    await this.saveToken(response.accessToken, response.expiresInSeconds);

    return response;
  }

  static async signup(data: SignupRequest): Promise<SignupResponse> {
    const response = await this.request<SignupResponse>("/v1/register", {
      method: "POST",
      body: JSON.stringify(data),
    });

    console.log("signup response", response);

    await this.saveToken(response.accessToken, response.expiresInSeconds);
    return response;
  }

  static async getProfile(): Promise<ProfileResponse> {
    const response = await this.request<ProfileResponse>("/v1/profile");
    console.log("profile response", response);
    return response;
  }

  static async logout(): Promise<void> {
    await this.clearToken();
    queryClient.clear();
    router.replace("/");
  }

  static async getCards(): Promise<CardsResponse> {
    const response = await this.request<CardsResponse>("/v1/cards");
    console.log("cards response", response);
    return response;
  }

  static async getCard(id: string): Promise<CardResponse> {
    const response = await this.request<CardResponse>(`/v1/card/${id}`);
    console.log("card response", response);
    return response;
  }

  static async createCard(data: CreateCardRequest): Promise<CardResponse> {
    const response = await this.request<CardResponse>("/v1/card", {
      method: "POST",
      body: JSON.stringify(data),
    });

    console.log("create patient response", response);

    return response;
  }

  static async uploadSpeechToText(
    audioUri: string
  ): Promise<SpeechToTextResponse> {
    console.log("Uploading speech to text with URI:", audioUri);

    // Create FormData for multipart upload
    const formData = new FormData();

    // Determine file type based on extension in URI
    const isWav = audioUri.includes(".wav");
    const fileExtension = isWav ? "wav" : "m4a";
    const mimeType = isWav ? "audio/wav" : "audio/m4a";

    // Create the file object for React Native
    const fileObject = {
      uri: audioUri,
      type: mimeType,
      name: `recording.${fileExtension}`,
    } as any;

    console.log("File object being uploaded:", fileObject);

    // Append the audio file
    formData.append("file", fileObject);

    console.log("FormData created, making API request to /v1/speechtotext");

    const response = await this.request<SpeechToTextResponse>(
      "/v1/speechtotext",
      {
        method: "POST",
        body: formData,
        headers: {
          // Don't set Content-Type, let the browser set it with boundary
        },
      }
    );

    console.log("Speech to text response:", response);

    return response;
  }

  static async getCardFiles(cardId: string): Promise<CardFilesResponse> {
    const response = await this.request<CardFilesResponse>(
      `/v1/card/${cardId}/files`
    );
    console.log("card files response", response);
    return response;
  }

  static async getFileUploader(
    cardId: string,
    data: FileUploaderRequest
  ): Promise<FileUploaderResponse> {
    const response = await this.request<FileUploaderResponse>(
      `/v1/card/${cardId}/file-uploader`,
      {
        method: "POST",
        body: JSON.stringify(data),
      }
    );
    console.log("file uploader response", response);
    return response;
  }

  static async analyzeFile(
    cardId: string,
    audioUri: string,
    analysisType: string = "clinical_note"
  ): Promise<AnalyzeFileResponse> {
    console.log("Analyzing file for card", cardId, audioUri, analysisType);

    const formData = new FormData();

    // Determine file type based on extension
    const isWav = audioUri.includes(".wav");
    const fileExtension = isWav ? "wav" : "m4a";
    const mimeType = isWav ? "audio/wav" : "audio/m4a";

    const fileObject = {
      uri: audioUri,
      type: mimeType,
      name: `recording.${fileExtension}`,
    } as any;

    formData.append("file", fileObject);
    formData.append("analysisType", analysisType);

    const response = await this.request<AnalyzeFileResponse>(
      `/v1/card/${cardId}/analyze-file`,
      {
        method: "POST",
        body: formData,
        headers: {},
      }
    );

    console.log("analyzeFile response", response);
    return response;
  }

  static async analyzeFileWithObjectName(
    cardId: string,
    data: AnalyzeFileWithObjectNameRequest
  ): Promise<AnalyzeFileResponse> {
    console.log("Analyzing file with object name for card", cardId, data);

    const formData = new FormData();
    formData.append("analysisType", data.analysisType);
    formData.append("objectName", data.objectName);

    const response = await this.request<AnalyzeFileResponse>(
      `/v1/card/${cardId}/analyze-file`,
      {
        method: "POST",
        body: formData,
      }
    );

    console.log("analyzeFile with objectName response", response);
    return response;
  }

  static async getCardFile(fileId: string): Promise<CardFileResponse> {
    const response = await this.request<CardFileResponse>(
      `/v1/card-file/${fileId}`
    );
    return response;
  }

  // Fetch LLM conversation messages for a given card
  static async getConversationMessages(
    cardId: string,
    limit: number = 100
  ): Promise<ConversationMessagesResponse> {
    const response = await this.request<ConversationMessagesResponse>(
      `/v1/card/${cardId}/llm/conversations/messages?limit=${limit}`
    );
    console.log("conversation messages response", response);
    return response;
  }

  static async postChatMessage(
    cardId: string,
    data: ChatMessageRequest
  ): Promise<ChatMessageResponse> {
    const response = await this.request<ChatMessageResponse>(
      `/v1/card/${cardId}/llm/chat-message`,
      {
        method: "POST",
        body: JSON.stringify(data),
      }
    );
    console.log("chat message response", response);
    return response;
  }

  static async regenerateCardFile(
    cardFileId: string
  ): Promise<{ success: boolean }> {
    const response = await this.request<{ success: boolean }>(
      `/v1/card-file/${cardFileId}/regenerate`,
      {
        method: "POST",
      }
    );
    console.log("regenerate card file response", response);
    return response;
  }

  static async deleteCard(id: string): Promise<{ success: boolean }> {
    const response = await this.request<{ success: boolean }>(
      `/v1/card/${id}`,
      {
        method: "DELETE",
      }
    );
    console.log("delete card response", response);
    return response;
  }

  static async clearChatConversation(
    cardId: string
  ): Promise<{ success: boolean }> {
    const response = await this.request<{ success: boolean }>(
      `/v1/card/${cardId}/llm/chat-message`,
      {
        method: "DELETE",
      }
    );
    console.log("clear chat conversation response", response);
    return response;
  }
}

export function useLogin() {
  return useMutation<LoginResponse, Error, LoginRequest>({
    mutationFn: (data) => APIClient.login(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["profile"] });
    },
  });
}

export function useSignup() {
  return useMutation<SignupResponse, Error, SignupRequest>({
    mutationFn: (data) => APIClient.signup(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["profile"] });
    },
  });
}

export function useProfile() {
  return useQuery<ProfileResponse, Error>({
    queryKey: ["profile"],
    queryFn: () => APIClient.getProfile(),
    retry: false,
  });
}

export function useCards() {
  return useQuery<CardsResponse, Error>({
    queryKey: ["cards"],
    queryFn: () => APIClient.getCards(),
  });
}

export function useCard(id: string) {
  return useQuery<CardResponse, Error>({
    queryKey: ["card", id],
    queryFn: () => APIClient.getCard(id),
    enabled: !!id,
  });
}

export function useCreateCard() {
  return useMutation<CardResponse, Error, CreateCardRequest>({
    mutationFn: (data) => APIClient.createCard(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["cards"] });
    },
  });
}

export function useSpeechToText() {
  return useMutation<SpeechToTextResponse, Error, string>({
    mutationFn: (audioUri) => APIClient.uploadSpeechToText(audioUri),
  });
}

export function useCardFiles(cardId: string) {
  return useQuery<CardFilesResponse, Error>({
    queryKey: ["cardFiles", cardId],
    queryFn: () => APIClient.getCardFiles(cardId),
    enabled: !!cardId,
  });
}

export function useFileUploader(cardId: string) {
  return useMutation<FileUploaderResponse, Error, FileUploaderRequest>({
    mutationFn: (data) => APIClient.getFileUploader(cardId, data),
  });
}

export function useAnalyzeFile(cardId: string) {
  return useMutation<
    AnalyzeFileResponse,
    Error,
    { audioUri: string; analysisType?: string }
  >({
    mutationFn: ({ audioUri, analysisType }) =>
      APIClient.analyzeFile(cardId, audioUri, analysisType || "clinical_note"),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["cardFiles", cardId] });
    },
  });
}

export function useAnalyzeFileWithObjectName(cardId: string) {
  return useMutation<
    AnalyzeFileResponse,
    Error,
    AnalyzeFileWithObjectNameRequest
  >({
    mutationFn: (data) => APIClient.analyzeFileWithObjectName(cardId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["cardFiles", cardId] });
    },
  });
}

export function useCardFile(fileId: string) {
  return useQuery<CardFileResponse, APIError>({
    queryKey: ["cardFile", fileId],
    queryFn: () => APIClient.getCardFile(fileId),
    staleTime: 10 * 1000, // 10 seconds
    throwOnError: false,
  });
}

// Hook to get conversation messages for a card
export function useConversationMessages(cardId: string) {
  return useQuery<ConversationMessagesResponse, Error>({
    queryKey: ["conversationMessages", cardId],
    queryFn: () => APIClient.getConversationMessages(cardId),
    enabled: !!cardId,
  });
}

export function useChatMessage(cardId: string) {
  return useMutation<ChatMessageResponse, Error, ChatMessageRequest>({
    mutationFn: (data) => APIClient.postChatMessage(cardId, data),
  });
}

export function useRegenerateCardFile(cardId: string) {
  return useMutation<{ success: boolean; cardFileId: string }, Error, string>({
    mutationFn: async (cardFileId) => {
      const response = await APIClient.regenerateCardFile(cardFileId);
      return { ...response, cardFileId };
    },
    onSuccess: ({ cardFileId }) => {
      queryClient.invalidateQueries({ queryKey: ["cardFiles", cardId] });
      queryClient.invalidateQueries({ queryKey: ["cardFile", cardFileId] });
    },
  });
}

export function useDeleteCard() {
  return useMutation<{ success: boolean }, Error, string>({
    mutationFn: (cardId) => APIClient.deleteCard(cardId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["cards"] });
    },
  });
}

export function useClearChatConversation(cardId: string) {
  return useMutation<{ success: boolean }, Error, void>({
    mutationFn: () => APIClient.clearChatConversation(cardId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["conversationMessages", cardId],
      });
    },
  });
}

export { APIClient };
